import 'package:dento_support/features/app/bloc/app_bloc.dart';
import 'package:dento_support/features/patients/presentation/bloc/patient_list_bloc.dart';
import 'package:dento_support/injector.dart';
import 'package:flutter/foundation.dart';

/// Service to handle deferred initialization of heavy operations
/// after the app has successfully loaded and navigated from splash screen
class DeferredInitializationService {
  static bool _isInitialized = false;
  
  /// Initialize heavy operations that don't need to block app startup
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      debugPrint('Starting deferred initialization...');
      
      // Initialize FCM token update (non-blocking)
      _initializeFcmTokenUpdate();
      
      // Initialize patient data fetching (non-blocking)
      _initializePatientData();
      
      _isInitialized = true;
      debugPrint('Deferred initialization completed');
    } catch (e) {
      debugPrint('Error during deferred initialization: $e');
      // Don't throw - these are non-critical operations
    }
  }
  
  static void _initializeFcmTokenUpdate() {
    try {
      // Update FCM token in background
      Future.delayed(const Duration(seconds: 2), () {
        final appBloc = getIt<AppBloc>();
        appBloc.add(const UpdateFcmToken());
      });
    } catch (e) {
      debugPrint('Error initializing FCM token update: $e');
    }
  }
  
  static void _initializePatientData() {
    try {
      // Fetch patient data in background
      Future.delayed(const Duration(seconds: 1), () {
        final patientListBloc = getIt<PatientListBloc>();
        patientListBloc.add(const PatientsFetched());
        patientListBloc.add(const UpdateVisitorCount());
      });
    } catch (e) {
      debugPrint('Error initializing patient data: $e');
    }
  }
  
  /// Reset initialization state (useful for testing)
  static void reset() {
    _isInitialized = false;
  }
}
